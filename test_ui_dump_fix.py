#!/usr/bin/env python3
"""
测试UI dump功能修复
验证新的 _get_ui_dump_content 方法是否能解决超时问题
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


def test_ui_dump_methods():
    """测试不同的UI dump方法"""
    try:
        log.info("=" * 60)
        log.info("测试UI dump方法")
        log.info("=" * 60)
        
        base_test = BaseEllaTest()
        
        # 测试1: 新的_get_ui_dump_content方法
        log.info("测试1: 新的_get_ui_dump_content方法")
        start_time = time.time()
        ui_content = base_test._get_ui_dump_content(timeout=8, retry_count=3)
        end_time = time.time()
        
        if ui_content:
            content_length = len(ui_content)
            log.info(f"✅ 成功获取UI内容，长度: {content_length} 字符")
            log.info(f"⏱️ 耗时: {end_time - start_time:.2f} 秒")
            
            # 检查内容是否包含有用信息
            if "<?xml" in ui_content and "hierarchy" in ui_content:
                log.info("✅ UI内容格式正确")
                
                # 检查是否包含一些常见的UI元素
                common_elements = ["android.widget", "resource-id", "text", "bounds"]
                found_elements = [elem for elem in common_elements if elem in ui_content]
                log.info(f"✅ 找到常见UI元素: {found_elements}")
                
                return True, content_length, end_time - start_time
            else:
                log.warning("⚠️ UI内容格式可能有问题")
                return False, 0, end_time - start_time
        else:
            log.error("❌ 无法获取UI内容")
            return False, 0, end_time - start_time
            
    except Exception as e:
        log.error(f"测试UI dump方法异常: {e}")
        return False, 0, 0


def test_recent_page_detection():
    """测试Recent页面检测"""
    try:
        log.info("=" * 60)
        log.info("测试Recent页面检测")
        log.info("=" * 60)
        
        base_test = BaseEllaTest()
        
        # 先返回桌面
        base_test._return_to_home()
        time.sleep(2)
        
        # 测试桌面状态下的UI检测
        log.info("测试桌面状态下的UI检测...")
        ui_content = base_test._get_ui_dump_content(timeout=6, retry_count=2)
        
        if ui_content:
            # 检查桌面相关元素
            desktop_indicators = ["launcher", "桌面", "home", "wallpaper"]
            found_desktop = [ind for ind in desktop_indicators if ind.lower() in ui_content.lower()]
            log.info(f"桌面状态检测到的元素: {found_desktop}")
            
            # 尝试打开Recent页面
            log.info("尝试打开Recent页面...")
            success = base_test._open_recent_with_three_button()
            
            if success:
                log.info("✅ Recent页面打开成功")
                time.sleep(3)
                
                # 检测Recent页面的UI
                log.info("检测Recent页面的UI...")
                recent_ui_content = base_test._get_ui_dump_content(timeout=6, retry_count=2)
                
                if recent_ui_content:
                    recent_indicators = ["recent", "Recent", "最近", "任务", "清理", "清除"]
                    found_recent = [ind for ind in recent_indicators if ind in recent_ui_content]
                    log.info(f"Recent页面检测到的元素: {found_recent}")
                    
                    if found_recent:
                        log.info("✅ Recent页面UI检测成功")
                        result = True
                    else:
                        log.warning("⚠️ Recent页面UI检测未找到相关元素")
                        result = False
                else:
                    log.error("❌ 无法获取Recent页面UI内容")
                    result = False
                
                # 返回桌面
                base_test._return_to_home()
                time.sleep(1)
                
                return result
            else:
                log.error("❌ Recent页面打开失败")
                return False
        else:
            log.error("❌ 无法获取桌面UI内容")
            return False
            
    except Exception as e:
        log.error(f"测试Recent页面检测异常: {e}")
        return False


def test_clear_button_detection():
    """测试删除按钮检测"""
    try:
        log.info("=" * 60)
        log.info("测试删除按钮检测")
        log.info("=" * 60)
        
        base_test = BaseEllaTest()
        
        # 先打开Recent页面
        base_test._return_to_home()
        time.sleep(2)
        
        success = base_test._open_recent_with_three_button()
        if not success:
            log.error("无法打开Recent页面，跳过删除按钮检测")
            return False
        
        time.sleep(3)
        
        # 使用新方法检测删除按钮
        log.info("使用新方法检测删除按钮...")
        found = base_test._find_and_click_clear_button()
        
        if found:
            log.info("✅ 删除按钮检测和点击成功")
            result = True
        else:
            log.warning("⚠️ 删除按钮检测失败")
            result = False
        
        # 返回桌面
        base_test._return_to_home()
        time.sleep(1)
        
        return result
        
    except Exception as e:
        log.error(f"测试删除按钮检测异常: {e}")
        # 确保返回桌面
        try:
            base_test = BaseEllaTest()
            base_test._return_to_home()
        except:
            pass
        return False


def test_complete_recent_clear():
    """测试完整的Recent清理流程"""
    try:
        log.info("=" * 60)
        log.info("测试完整的Recent清理流程")
        log.info("=" * 60)
        
        base_test = BaseEllaTest()
        
        # 执行完整的清理流程
        cleared_count = base_test.clear_recent_apps()
        
        log.info(f"清理结果: {cleared_count} 个应用")
        
        return cleared_count > 0
        
    except Exception as e:
        log.error(f"测试完整清理流程异常: {e}")
        return False


def main():
    """主函数"""
    log.info("🚀 开始测试UI dump功能修复")
    
    results = []
    
    # 测试1: UI dump方法
    log.info("\n📱 测试1: UI dump方法")
    result1, content_length, duration = test_ui_dump_methods()
    results.append(("UI dump方法", result1))
    
    # 测试2: Recent页面检测
    log.info("\n📱 测试2: Recent页面检测")
    result2 = test_recent_page_detection()
    results.append(("Recent页面检测", result2))
    
    # 测试3: 删除按钮检测
    log.info("\n📱 测试3: 删除按钮检测")
    result3 = test_clear_button_detection()
    results.append(("删除按钮检测", result3))
    
    # 测试4: 完整清理流程
    log.info("\n📱 测试4: 完整清理流程")
    result4 = test_complete_recent_clear()
    results.append(("完整清理流程", result4))
    
    # 输出测试结果
    log.info("\n" + "=" * 60)
    log.info("测试结果汇总")
    log.info("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        log.info(f"{test_name}: {status}")
    
    # 显示性能信息
    if result1:
        log.info(f"\nUI dump性能信息:")
        log.info(f"  内容长度: {content_length} 字符")
        log.info(f"  获取耗时: {duration:.2f} 秒")
    
    # 计算通过率
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    pass_rate = (passed_count / total_count) * 100
    
    log.info(f"\n通过率: {passed_count}/{total_count} ({pass_rate:.1f}%)")
    
    if pass_rate >= 75:
        log.info("🎉 UI dump功能修复成功！")
        return True
    else:
        log.warning("⚠️ UI dump功能仍需进一步优化")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
