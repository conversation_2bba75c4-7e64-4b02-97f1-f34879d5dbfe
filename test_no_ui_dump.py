#!/usr/bin/env python3
"""
测试不依赖UI dump的Recent页面清理功能
验证修复后的方法是否能正常工作
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


def test_navigation_detection():
    """测试导航模式检测（不依赖UI dump）"""
    try:
        log.info("=" * 50)
        log.info("测试导航模式检测")
        log.info("=" * 50)
        
        base_test = BaseEllaTest()
        navigation_mode = base_test._detect_navigation_mode()
        
        log.info(f"检测到的导航模式: {navigation_mode}")
        
        if navigation_mode == "three_button":
            log.info("✅ 正确检测到三键导航")
            return True
        else:
            log.warning(f"⚠️ 检测结果: {navigation_mode}")
            return False
            
    except Exception as e:
        log.error(f"导航模式检测异常: {e}")
        return False


def test_recent_page_opening():
    """测试Recent页面打开（不依赖UI dump）"""
    try:
        log.info("=" * 50)
        log.info("测试Recent页面打开")
        log.info("=" * 50)
        
        base_test = BaseEllaTest()
        
        # 先返回桌面
        base_test._return_to_home()
        time.sleep(2)
        
        # 尝试打开Recent页面
        log.info("尝试打开Recent页面...")
        success = base_test._open_recent_with_three_button()
        
        if success:
            log.info("✅ Recent页面打开成功")
            time.sleep(2)
            # 返回桌面
            base_test._return_to_home()
            return True
        else:
            log.warning("❌ Recent页面打开失败")
            return False
            
    except Exception as e:
        log.error(f"Recent页面打开测试异常: {e}")
        return False


def test_clear_button_clicking():
    """测试删除按钮点击（不依赖UI dump）"""
    try:
        log.info("=" * 50)
        log.info("测试删除按钮点击")
        log.info("=" * 50)
        
        base_test = BaseEllaTest()
        
        # 先打开Recent页面
        base_test._return_to_home()
        time.sleep(2)
        
        success = base_test._open_recent_with_three_button()
        if not success:
            log.error("无法打开Recent页面，跳过删除按钮测试")
            return False
        
        time.sleep(3)
        
        # 尝试点击删除按钮
        log.info("尝试点击删除按钮...")
        clear_success = base_test._find_and_click_clear_button()
        
        if clear_success:
            log.info("✅ 删除按钮点击成功")
            result = True
        else:
            log.warning("⚠️ 删除按钮点击失败")
            result = False
        
        # 返回桌面
        base_test._return_to_home()
        time.sleep(1)
        
        return result
        
    except Exception as e:
        log.error(f"删除按钮点击测试异常: {e}")
        # 确保返回桌面
        try:
            base_test = BaseEllaTest()
            base_test._return_to_home()
        except:
            pass
        return False


def test_complete_clear_flow():
    """测试完整的清理流程"""
    try:
        log.info("=" * 50)
        log.info("测试完整的清理流程")
        log.info("=" * 50)
        
        base_test = BaseEllaTest()
        
        # 执行完整的清理流程
        cleared_count = base_test.clear_recent_apps()
        
        log.info(f"清理结果: {cleared_count} 个应用")
        
        if cleared_count > 0:
            log.info("✅ 完整清理流程成功")
            return True
        else:
            log.warning("⚠️ 完整清理流程未清理任何应用")
            return False
            
    except Exception as e:
        log.error(f"完整清理流程测试异常: {e}")
        return False


def test_basic_adb_commands():
    """测试基本的ADB命令是否正常"""
    try:
        log.info("=" * 50)
        log.info("测试基本ADB命令")
        log.info("=" * 50)
        
        import subprocess
        
        # 测试1: 返回桌面
        log.info("测试返回桌面...")
        result = subprocess.run(
            ["adb", "shell", "input", "keyevent", "KEYCODE_HOME"],
            capture_output=True,
            text=True,
            timeout=3
        )
        
        if result.returncode == 0:
            log.info("✅ 返回桌面命令执行成功")
            home_success = True
        else:
            log.error("❌ 返回桌面命令执行失败")
            home_success = False
        
        time.sleep(2)
        
        # 测试2: Recent按键
        log.info("测试Recent按键...")
        result = subprocess.run(
            ["adb", "shell", "input", "keyevent", "KEYCODE_APP_SWITCH"],
            capture_output=True,
            text=True,
            timeout=3
        )
        
        if result.returncode == 0:
            log.info("✅ Recent按键命令执行成功")
            recent_success = True
        else:
            log.error("❌ Recent按键命令执行失败")
            recent_success = False
        
        time.sleep(2)
        
        # 测试3: 屏幕点击
        log.info("测试屏幕点击...")
        result = subprocess.run(
            ["adb", "shell", "input", "tap", "540", "1000"],
            capture_output=True,
            text=True,
            timeout=3
        )
        
        if result.returncode == 0:
            log.info("✅ 屏幕点击命令执行成功")
            tap_success = True
        else:
            log.error("❌ 屏幕点击命令执行失败")
            tap_success = False
        
        # 返回桌面
        subprocess.run(
            ["adb", "shell", "input", "keyevent", "KEYCODE_HOME"],
            capture_output=True,
            text=True,
            timeout=3
        )
        
        return home_success and recent_success and tap_success
        
    except Exception as e:
        log.error(f"基本ADB命令测试异常: {e}")
        return False


def main():
    """主函数"""
    log.info("🚀 开始测试不依赖UI dump的Recent页面清理功能")
    
    results = []
    
    # 测试1: 基本ADB命令
    log.info("\n📱 测试1: 基本ADB命令")
    result1 = test_basic_adb_commands()
    results.append(("基本ADB命令", result1))
    
    # 测试2: 导航模式检测
    log.info("\n📱 测试2: 导航模式检测")
    result2 = test_navigation_detection()
    results.append(("导航模式检测", result2))
    
    # 测试3: Recent页面打开
    log.info("\n📱 测试3: Recent页面打开")
    result3 = test_recent_page_opening()
    results.append(("Recent页面打开", result3))
    
    # 测试4: 删除按钮点击
    log.info("\n📱 测试4: 删除按钮点击")
    result4 = test_clear_button_clicking()
    results.append(("删除按钮点击", result4))
    
    # 测试5: 完整清理流程
    log.info("\n📱 测试5: 完整清理流程")
    result5 = test_complete_clear_flow()
    results.append(("完整清理流程", result5))
    
    # 输出测试结果
    log.info("\n" + "=" * 60)
    log.info("测试结果汇总")
    log.info("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        log.info(f"{test_name}: {status}")
    
    # 计算通过率
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    pass_rate = (passed_count / total_count) * 100
    
    log.info(f"\n通过率: {passed_count}/{total_count} ({pass_rate:.1f}%)")
    
    if pass_rate >= 60:  # 至少60%通过
        log.info("🎉 不依赖UI dump的功能基本正常！")
        return True
    else:
        log.warning("⚠️ 功能仍需进一步调试")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
