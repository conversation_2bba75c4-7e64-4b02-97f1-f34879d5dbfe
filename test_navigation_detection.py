#!/usr/bin/env python3
"""
测试导航模式检测功能
验证 _detect_navigation_mode 方法是否能正确检测三键导航
"""

import sys
import os
import time
import subprocess

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


def test_system_settings():
    """测试系统设置检测"""
    try:
        log.info("=" * 50)
        log.info("测试系统设置检测")
        log.info("=" * 50)
        
        result = subprocess.run(
            ["adb", "shell", "settings", "get", "secure", "navigation_mode"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            mode_value = result.stdout.strip()
            log.info(f"navigation_mode值: '{mode_value}'")
            
            if mode_value == "2":
                log.info("✅ 系统设置显示：三键导航")
                return "three_button"
            elif mode_value == "1":
                log.info("✅ 系统设置显示：两键导航")
                return "two_button"
            elif mode_value == "0":
                log.info("✅ 系统设置显示：手势导航")
                return "gesture"
            else:
                log.warning(f"⚠️ 未知的navigation_mode值: {mode_value}")
                return "unknown"
        else:
            log.error("❌ 无法获取navigation_mode设置")
            return "error"
            
    except Exception as e:
        log.error(f"系统设置检测异常: {e}")
        return "error"


def test_ui_detection():
    """测试UI检测"""
    try:
        log.info("=" * 50)
        log.info("测试UI检测")
        log.info("=" * 50)
        
        result = subprocess.run(
            ["adb", "shell", "uiautomator", "dump", "/sdcard/ui_dump.xml"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            result2 = subprocess.run(
                ["adb", "shell", "cat", "/sdcard/ui_dump.xml"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result2.returncode == 0:
                ui_content = result2.stdout
                
                # 检查三键导航的特征元素
                three_button_indicators = [
                    "com.android.systemui:id/recent_apps",
                    "com.android.systemui:id/home", 
                    "com.android.systemui:id/back",
                    "navigation_bar_view"
                ]
                
                found_indicators = []
                for indicator in three_button_indicators:
                    if indicator in ui_content:
                        found_indicators.append(indicator)
                
                log.info(f"找到的三键导航元素: {found_indicators}")
                
                if len(found_indicators) >= 2:
                    log.info("✅ UI检测显示：三键导航")
                    return "three_button"
                elif found_indicators:
                    log.warning("⚠️ 部分三键导航元素，可能是三键导航")
                    return "partial_three_button"
                else:
                    log.info("❌ UI检测显示：可能是手势导航")
                    return "gesture"
            else:
                log.error("❌ 无法读取UI dump文件")
                return "error"
        else:
            log.error("❌ 无法生成UI dump")
            return "error"
            
    except Exception as e:
        log.error(f"UI检测异常: {e}")
        return "error"


def test_hardware_properties():
    """测试硬件属性检测"""
    try:
        log.info("=" * 50)
        log.info("测试硬件属性检测")
        log.info("=" * 50)
        
        # 检查 qemu.hw.mainkeys
        result = subprocess.run(
            ["adb", "shell", "getprop", "qemu.hw.mainkeys"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            mainkeys_value = result.stdout.strip()
            log.info(f"qemu.hw.mainkeys值: '{mainkeys_value}'")
            
            if mainkeys_value == "0":
                log.info("✅ 硬件属性显示：软件导航栏（可能是三键）")
                return "three_button"
            elif mainkeys_value == "1":
                log.info("✅ 硬件属性显示：硬件按键")
                return "hardware_keys"
            else:
                log.warning(f"⚠️ 未知的mainkeys值: {mainkeys_value}")
                return "unknown"
        else:
            log.error("❌ 无法获取qemu.hw.mainkeys属性")
            return "error"
            
    except Exception as e:
        log.error(f"硬件属性检测异常: {e}")
        return "error"


def test_base_class_detection():
    """测试基类检测方法"""
    try:
        log.info("=" * 50)
        log.info("测试BaseEllaTest类的检测方法")
        log.info("=" * 50)
        
        base_test = BaseEllaTest()
        navigation_mode = base_test._detect_navigation_mode()
        
        log.info(f"BaseEllaTest检测结果: {navigation_mode}")
        
        return navigation_mode
        
    except Exception as e:
        log.error(f"基类检测异常: {e}")
        return "error"


def test_window_manager():
    """测试窗口管理器检测"""
    try:
        log.info("=" * 50)
        log.info("测试窗口管理器检测")
        log.info("=" * 50)
        
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "window", "|", "grep", "-i", "navigation"],
            capture_output=True,
            text=True,
            timeout=8,
            shell=True
        )
        
        if result.returncode == 0 and result.stdout.strip():
            window_output = result.stdout
            log.info(f"窗口管理器导航信息（前200字符）: {window_output[:200]}...")
            
            # 检查是否包含导航栏相关信息
            if any(keyword in window_output.lower() for keyword in 
                   ["navigationbar", "navigation_bar", "navbar"]):
                log.info("✅ 窗口管理器检测显示：有导航栏（可能是三键）")
                return "three_button"
            else:
                log.info("❌ 窗口管理器检测显示：无明显导航栏信息")
                return "gesture"
        else:
            log.error("❌ 无法获取窗口管理器导航信息")
            return "error"
            
    except Exception as e:
        log.error(f"窗口管理器检测异常: {e}")
        return "error"


def main():
    """主函数"""
    log.info("🚀 开始测试导航模式检测功能")
    log.info("用户确认当前设备使用三键导航")
    
    results = []
    
    # 测试1: 系统设置检测
    log.info("\n📱 测试1: 系统设置检测")
    result1 = test_system_settings()
    results.append(("系统设置检测", result1))
    
    # 测试2: UI检测
    log.info("\n📱 测试2: UI检测")
    result2 = test_ui_detection()
    results.append(("UI检测", result2))
    
    # 测试3: 硬件属性检测
    log.info("\n📱 测试3: 硬件属性检测")
    result3 = test_hardware_properties()
    results.append(("硬件属性检测", result3))
    
    # 测试4: 窗口管理器检测
    log.info("\n📱 测试4: 窗口管理器检测")
    result4 = test_window_manager()
    results.append(("窗口管理器检测", result4))
    
    # 测试5: 基类检测方法
    log.info("\n📱 测试5: BaseEllaTest类检测")
    result5 = test_base_class_detection()
    results.append(("BaseEllaTest检测", result5))
    
    # 输出测试结果
    log.info("\n" + "=" * 60)
    log.info("检测结果汇总")
    log.info("=" * 60)
    
    for test_name, result in results:
        if result == "three_button":
            status = "✅ 正确（三键导航）"
        elif result in ["partial_three_button", "hardware_keys"]:
            status = "⚠️ 部分正确"
        elif result in ["gesture", "two_button"]:
            status = "❌ 错误"
        else:
            status = f"❓ 未知 ({result})"
        
        log.info(f"{test_name}: {status}")
    
    # 统计正确检测的方法
    correct_count = sum(1 for _, result in results if result == "three_button")
    total_count = len(results)
    accuracy = (correct_count / total_count) * 100
    
    log.info(f"\n检测准确率: {correct_count}/{total_count} ({accuracy:.1f}%)")
    
    if correct_count >= 1:
        log.info("🎉 至少有一种方法能正确检测三键导航！")
        return True
    else:
        log.warning("⚠️ 所有检测方法都无法正确识别三键导航")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
