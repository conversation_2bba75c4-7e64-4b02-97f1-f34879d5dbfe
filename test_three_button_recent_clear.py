#!/usr/bin/env python3
"""
测试三键导航下的Recent页面清理功能
专门针对三键导航模式进行测试
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


def test_three_button_recent_clear():
    """测试三键导航下的Recent页面清理"""
    try:
        log.info("=" * 60)
        log.info("测试三键导航下的Recent页面清理")
        log.info("=" * 60)
        
        # 创建测试实例
        base_test = BaseEllaTest()
        
        # 步骤1: 返回桌面
        log.info("步骤1: 返回桌面")
        base_test._return_to_home()
        time.sleep(2)
        
        # 步骤2: 强制使用三键导航打开Recent页面
        log.info("步骤2: 使用三键导航打开Recent页面")
        success = base_test._open_recent_with_three_button()
        
        if success:
            log.info("✅ Recent页面打开成功")
            time.sleep(3)  # 等待页面稳定
            
            # 步骤3: 查找并点击删除按钮
            log.info("步骤3: 查找并点击删除按钮")
            clear_success = base_test._find_and_click_clear_button()
            
            if clear_success:
                log.info("✅ 成功点击删除按钮")
                time.sleep(3)  # 等待清理完成
                result = "SUCCESS"
            else:
                log.warning("⚠️ 未找到或无法点击删除按钮")
                result = "BUTTON_NOT_FOUND"
            
            # 步骤4: 返回桌面
            log.info("步骤4: 返回桌面")
            base_test._return_to_home()
            time.sleep(1)
            
        else:
            log.error("❌ Recent页面打开失败")
            result = "OPEN_FAILED"
        
        return result
        
    except Exception as e:
        log.error(f"测试异常: {e}")
        return "ERROR"


def test_recent_button_detection():
    """测试Recent按钮检测"""
    try:
        log.info("=" * 60)
        log.info("测试Recent按钮检测")
        log.info("=" * 60)
        
        base_test = BaseEllaTest()
        
        # 返回桌面
        base_test._return_to_home()
        time.sleep(2)
        
        # 获取UI结构并查找Recent按钮
        import subprocess
        result = subprocess.run(
            ["adb", "shell", "uiautomator", "dump", "/sdcard/ui_dump.xml"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            result2 = subprocess.run(
                ["adb", "shell", "cat", "/sdcard/ui_dump.xml"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result2.returncode == 0:
                ui_content = result2.stdout
                
                # 检查是否包含Recent按钮的resource-id
                if "com.android.systemui:id/recent_apps" in ui_content:
                    log.info("✅ 找到Recent按钮 (com.android.systemui:id/recent_apps)")
                    return True
                else:
                    log.warning("❌ 未找到Recent按钮的resource-id")
                    
                    # 查找其他可能的Recent按钮标识
                    recent_indicators = [
                        "recent", "Recent", "最近", "任务", "task", "Task"
                    ]
                    
                    found_indicators = []
                    for indicator in recent_indicators:
                        if indicator in ui_content:
                            found_indicators.append(indicator)
                    
                    if found_indicators:
                        log.info(f"找到可能的Recent相关元素: {found_indicators}")
                        return True
                    else:
                        log.warning("未找到任何Recent相关元素")
                        return False
        
        return False
        
    except Exception as e:
        log.error(f"Recent按钮检测异常: {e}")
        return False


def test_clear_button_detection():
    """测试删除按钮检测"""
    try:
        log.info("=" * 60)
        log.info("测试删除按钮检测")
        log.info("=" * 60)
        
        base_test = BaseEllaTest()
        
        # 先打开Recent页面
        base_test._return_to_home()
        time.sleep(2)
        
        success = base_test._open_recent_with_three_button()
        if not success:
            log.error("无法打开Recent页面，跳过删除按钮检测")
            return False
        
        time.sleep(3)  # 等待页面稳定
        
        # 获取UI结构并查找删除按钮
        import subprocess
        result = subprocess.run(
            ["adb", "shell", "uiautomator", "dump", "/sdcard/ui_dump.xml"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            result2 = subprocess.run(
                ["adb", "shell", "cat", "/sdcard/ui_dump.xml"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result2.returncode == 0:
                ui_content = result2.stdout
                
                # 检查是否包含删除按钮的resource-id
                if "com.transsion.hilauncher:id/ts_btn_recents_clear" in ui_content:
                    log.info("✅ 找到删除按钮 (com.transsion.hilauncher:id/ts_btn_recents_clear)")
                    found = True
                else:
                    log.warning("❌ 未找到删除按钮的resource-id")
                    found = False
                    
                    # 查找其他可能的删除按钮标识
                    clear_indicators = [
                        "清理", "清除", "Clear", "Delete", "清空", "全部清理", "clean"
                    ]
                    
                    found_indicators = []
                    for indicator in clear_indicators:
                        if indicator in ui_content:
                            found_indicators.append(indicator)
                    
                    if found_indicators:
                        log.info(f"找到可能的清理相关元素: {found_indicators}")
                        found = True
                    else:
                        log.warning("未找到任何清理相关元素")
                
                # 返回桌面
                base_test._return_to_home()
                time.sleep(1)
                
                return found
        
        return False
        
    except Exception as e:
        log.error(f"删除按钮检测异常: {e}")
        # 确保返回桌面
        try:
            base_test = BaseEllaTest()
            base_test._return_to_home()
        except:
            pass
        return False


def main():
    """主函数"""
    log.info("🚀 开始测试三键导航下的Recent页面清理功能")
    
    results = []
    
    # 测试1: Recent按钮检测
    log.info("\n📱 测试1: Recent按钮检测")
    result1 = test_recent_button_detection()
    results.append(("Recent按钮检测", result1))
    
    # 测试2: 删除按钮检测
    log.info("\n📱 测试2: 删除按钮检测")
    result2 = test_clear_button_detection()
    results.append(("删除按钮检测", result2))
    
    # 测试3: 完整清理流程
    log.info("\n📱 测试3: 完整清理流程")
    result3 = test_three_button_recent_clear()
    results.append(("完整清理流程", result3 == "SUCCESS"))
    
    # 输出测试结果
    log.info("\n" + "=" * 60)
    log.info("测试结果汇总")
    log.info("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        log.info(f"{test_name}: {status}")
    
    # 特殊处理完整清理流程的结果
    if isinstance(result3, str) and result3 != "SUCCESS":
        log.info(f"完整清理流程详细结果: {result3}")
    
    # 计算通过率
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    pass_rate = (passed_count / total_count) * 100
    
    log.info(f"\n通过率: {passed_count}/{total_count} ({pass_rate:.1f}%)")
    
    if pass_rate >= 66:  # 至少2/3通过
        log.info("🎉 测试整体通过！")
        return True
    else:
        log.warning("⚠️ 测试存在问题，需要进一步调试")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
