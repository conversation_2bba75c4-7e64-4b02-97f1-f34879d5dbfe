#!/usr/bin/env python3
"""
简单的Recent页面清理测试
专门针对三键导航，使用最基本的方法
"""

import sys
import os
import time
import subprocess

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


def simple_return_home():
    """简单返回桌面"""
    try:
        log.info("返回桌面...")
        result = subprocess.run(
            ["adb", "shell", "input", "keyevent", "KEYCODE_HOME"],
            capture_output=True,
            text=True,
            timeout=3
        )
        time.sleep(2)
        log.info("✅ 返回桌面完成")
        return True
    except Exception as e:
        log.error(f"返回桌面失败: {e}")
        return False


def simple_open_recent():
    """简单打开Recent页面"""
    try:
        log.info("尝试打开Recent页面...")
        
        # 方法1: KEYCODE_APP_SWITCH
        log.info("使用KEYCODE_APP_SWITCH...")
        result = subprocess.run(
            ["adb", "shell", "input", "keyevent", "KEYCODE_APP_SWITCH"],
            capture_output=True,
            text=True,
            timeout=3
        )
        time.sleep(3)
        
        # 简单验证：检查是否还在桌面
        result2 = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "activities"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result2.returncode == 0:
            activity_output = result2.stdout
            # 如果不包含launcher，说明可能已经离开桌面
            if "launcher" not in activity_output.lower():
                log.info("✅ 可能已打开Recent页面（不在桌面）")
                return True
        
        # 方法2: KEYCODE_MENU
        log.info("使用KEYCODE_MENU...")
        result = subprocess.run(
            ["adb", "shell", "input", "keyevent", "KEYCODE_MENU"],
            capture_output=True,
            text=True,
            timeout=3
        )
        time.sleep(3)
        
        # 再次验证
        result3 = subprocess.run(
            ["adb", "shell", "dumpsys", "activity", "activities"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result3.returncode == 0:
            activity_output = result3.stdout
            if "launcher" not in activity_output.lower():
                log.info("✅ 可能已打开Recent页面（不在桌面）")
                return True
        
        log.warning("❌ 无法确认Recent页面是否打开")
        return False
        
    except Exception as e:
        log.error(f"打开Recent页面失败: {e}")
        return False


def simple_click_clear():
    """简单点击清理按钮"""
    try:
        log.info("尝试点击清理按钮...")
        
        # 方法1: 尝试点击屏幕底部中央（常见的清理按钮位置）
        positions = [
            (540, 1800),  # 底部中央
            (540, 1600),  # 中下位置
            (1000, 1800), # 底部右侧
            (200, 1800),  # 底部左侧
        ]
        
        for x, y in positions:
            log.info(f"尝试点击位置: ({x}, {y})")
            result = subprocess.run(
                ["adb", "shell", "input", "tap", str(x), str(y)],
                capture_output=True,
                text=True,
                timeout=3
            )
            time.sleep(2)
            
            # 检查是否回到桌面（清理成功的标志）
            result2 = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result2.returncode == 0:
                activity_output = result2.stdout
                if "launcher" in activity_output.lower():
                    log.info(f"✅ 点击位置 ({x}, {y}) 可能成功，已回到桌面")
                    return True
        
        log.warning("❌ 所有位置都无法成功点击清理按钮")
        return False
        
    except Exception as e:
        log.error(f"点击清理按钮失败: {e}")
        return False


def test_complete_flow():
    """测试完整流程"""
    try:
        log.info("=" * 60)
        log.info("测试完整的Recent页面清理流程")
        log.info("=" * 60)
        
        # 步骤1: 返回桌面
        log.info("步骤1: 返回桌面")
        if not simple_return_home():
            return "HOME_FAILED"
        
        # 步骤2: 打开Recent页面
        log.info("步骤2: 打开Recent页面")
        if not simple_open_recent():
            return "OPEN_FAILED"
        
        # 步骤3: 点击清理按钮
        log.info("步骤3: 点击清理按钮")
        if not simple_click_clear():
            # 如果点击失败，手动返回桌面
            simple_return_home()
            return "CLEAR_FAILED"
        
        # 步骤4: 确保回到桌面
        log.info("步骤4: 确保回到桌面")
        simple_return_home()
        
        log.info("✅ 完整流程测试成功")
        return "SUCCESS"
        
    except Exception as e:
        log.error(f"完整流程测试异常: {e}")
        # 确保返回桌面
        try:
            simple_return_home()
        except:
            pass
        return "ERROR"


def test_using_base_class():
    """使用基类方法测试"""
    try:
        log.info("=" * 60)
        log.info("使用BaseEllaTest类测试")
        log.info("=" * 60)
        
        base_test = BaseEllaTest()
        cleared_count = base_test.clear_recent_apps()
        
        log.info(f"清理结果: {cleared_count} 个应用")
        
        return cleared_count > 0
        
    except Exception as e:
        log.error(f"基类测试异常: {e}")
        return False


def main():
    """主函数"""
    log.info("🚀 开始简单的Recent页面清理测试")
    
    results = []
    
    # 测试1: 完整流程测试
    log.info("\n📱 测试1: 完整流程测试")
    result1 = test_complete_flow()
    results.append(("完整流程测试", result1 == "SUCCESS"))
    
    # 测试2: 使用基类方法
    log.info("\n📱 测试2: 使用基类方法")
    result2 = test_using_base_class()
    results.append(("基类方法测试", result2))
    
    # 输出测试结果
    log.info("\n" + "=" * 60)
    log.info("测试结果汇总")
    log.info("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        log.info(f"{test_name}: {status}")
    
    # 特殊处理完整流程的结果
    if isinstance(result1, str) and result1 != "SUCCESS":
        log.info(f"完整流程详细结果: {result1}")
    
    # 计算通过率
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    pass_rate = (passed_count / total_count) * 100
    
    log.info(f"\n通过率: {passed_count}/{total_count} ({pass_rate:.1f}%)")
    
    if pass_rate >= 50:  # 至少一半通过
        log.info("🎉 测试基本通过！")
        return True
    else:
        log.warning("⚠️ 测试需要进一步调试")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
