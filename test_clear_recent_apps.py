#!/usr/bin/env python3
"""
测试 clear_recent_apps 方法的脚本
用于验证Recent页面清理功能是否正常工作
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


def test_clear_recent_apps():
    """测试清理Recent页面应用的功能"""
    try:
        log.info("=" * 60)
        log.info("开始测试 clear_recent_apps 方法")
        log.info("=" * 60)
        
        # 创建测试实例
        base_test = BaseEllaTest()
        
        # 执行清理
        cleared_count = base_test.clear_recent_apps()
        
        log.info("=" * 60)
        log.info(f"测试完成，清理了 {cleared_count} 个应用")
        log.info("=" * 60)
        
        return cleared_count > 0
        
    except Exception as e:
        log.error(f"测试异常: {e}")
        return False


def test_navigation_detection():
    """测试导航模式检测"""
    try:
        log.info("=" * 60)
        log.info("测试导航模式检测")
        log.info("=" * 60)
        
        base_test = BaseEllaTest()
        navigation_mode = base_test._detect_navigation_mode()
        
        log.info(f"检测到的导航模式: {navigation_mode}")
        
        return navigation_mode is not None
        
    except Exception as e:
        log.error(f"导航模式检测异常: {e}")
        return False


def test_recent_page_opening():
    """测试Recent页面打开功能"""
    try:
        log.info("=" * 60)
        log.info("测试Recent页面打开功能")
        log.info("=" * 60)
        
        base_test = BaseEllaTest()
        
        # 先返回桌面
        base_test._return_to_home()
        time.sleep(1)
        
        # 检测导航模式
        navigation_mode = base_test._detect_navigation_mode()
        log.info(f"导航模式: {navigation_mode}")
        
        # 根据导航模式测试打开Recent页面
        if navigation_mode == "three_button":
            success = base_test._open_recent_with_three_button()
        else:
            success = base_test._open_recent_with_gesture()
        
        if success:
            log.info("✅ Recent页面打开成功")
            time.sleep(2)
            # 返回桌面
            base_test._return_to_home()
        else:
            log.warning("❌ Recent页面打开失败")
        
        return success
        
    except Exception as e:
        log.error(f"Recent页面打开测试异常: {e}")
        return False


def main():
    """主函数"""
    log.info("🚀 开始测试 clear_recent_apps 相关功能")
    
    results = []
    
    # 测试1: 导航模式检测
    log.info("\n📱 测试1: 导航模式检测")
    result1 = test_navigation_detection()
    results.append(("导航模式检测", result1))
    
    # 测试2: Recent页面打开
    log.info("\n📱 测试2: Recent页面打开")
    result2 = test_recent_page_opening()
    results.append(("Recent页面打开", result2))
    
    # 测试3: 完整清理流程
    log.info("\n📱 测试3: 完整清理流程")
    result3 = test_clear_recent_apps()
    results.append(("完整清理流程", result3))
    
    # 输出测试结果
    log.info("\n" + "=" * 60)
    log.info("测试结果汇总")
    log.info("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        log.info(f"{test_name}: {status}")
    
    # 计算通过率
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    pass_rate = (passed_count / total_count) * 100
    
    log.info(f"\n通过率: {passed_count}/{total_count} ({pass_rate:.1f}%)")
    
    if pass_rate >= 80:
        log.info("🎉 测试整体通过！")
        return True
    else:
        log.warning("⚠️ 测试存在问题，需要进一步调试")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
